"""
FastAPI webapp for DataHero4 deployment - platform-agnostic and production-ready.
"""
import os
import sys
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Configure logging
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# DEBUG: Log current working directory and Python path
logger.info(f"🔍 Current working directory: {os.getcwd()}")
logger.info(f"🔍 Python path: {sys.path}")
logger.info(f"🔍 Files in current directory: {os.listdir('.')}")

# DEBUG: Check if src directory exists
if os.path.exists('src'):
    logger.info(f"✅ src directory found, contents: {os.listdir('src')}")
else:
    logger.error("❌ src directory not found!")

# DEBUG: Check if src/interfaces exists
if os.path.exists('src/interfaces'):
    logger.info(f"✅ src/interfaces directory found, contents: {os.listdir('src/interfaces')}")
else:
    logger.error("❌ src/interfaces directory not found!")

try:
    logger.info("🔄 Attempting to import src.interfaces.api...")
    from src.interfaces.api import app as main_app
    logger.info("✅ Successfully imported main application")
except ImportError as e:
    logger.error(f"❌ Failed to import main application: {e}")
    logger.error(f"❌ Import error type: {type(e)}")
    logger.error(f"❌ Import error args: {e.args}")

    # Try to import step by step to isolate the issue
    try:
        logger.info("🔄 Testing import of src...")
        import src
        logger.info("✅ src import successful")
    except ImportError as e2:
        logger.error(f"❌ Failed to import src: {e2}")

    try:
        logger.info("🔄 Testing import of src.interfaces...")
        import src.interfaces
        logger.info("✅ src.interfaces import successful")
    except ImportError as e3:
        logger.error(f"❌ Failed to import src.interfaces: {e3}")

    sys.exit(1)

# Define lifespan for resource management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle with proper startup and shutdown."""
    # Initialize resources on startup
    logger.info("🚀 DataHero4 backend starting up...")
    logger.info(f"Environment: {os.getenv('ENVIRONMENT', 'development')}")
    logger.info(f"Log level: {os.getenv('LOG_LEVEL', 'INFO')}")
    
    # Add any startup logic here (database connections, cache warming, etc.)
    
    yield
    
    # Clean up resources on shutdown
    logger.info("🔄 DataHero4 backend shutting down gracefully...")
    # Add any cleanup logic here

# Create FastAPI app instance with lifespan
app = FastAPI(
    title="DataHero4 LangGraph API",
    description="DataHero4 LangGraph deployment with optimized workflow",
    version="4.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
)

# Add CORS middleware for production deployments
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Enhanced health check endpoint for load balancers
@app.get("/health")
async def health_check() -> JSONResponse:
    """
    Comprehensive health check endpoint for deployment platforms.

    Returns detailed health status for load balancers and monitoring systems.
    """
    try:
        health_data = {
            "status": "healthy",
            "service": "datahero4-backend",
            "version": "4.0.0",
            "environment": os.getenv("ENVIRONMENT", "development"),
            "port": os.getenv("PORT", "8000"),
            "timestamp": "2025-01-07T00:00:00Z"  # Would use actual timestamp in real implementation
        }

        # Add basic connectivity checks here if needed
        # For example: database connection, Redis connection, etc.

        return JSONResponse(
            status_code=200,
            content=health_data
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "datahero4-backend",
                "error": str(e)
            }
        )

# Readiness probe endpoint (for Kubernetes or advanced deployments)
@app.get("/ready")
async def readiness_check() -> JSONResponse:
    """Check if the application is ready to serve traffic."""
    try:
        # Add readiness checks here (database connectivity, required services, etc.)
        return JSONResponse(
            status_code=200,
            content={
                "status": "ready",
                "service": "datahero4-backend"
            }
        )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "service": "datahero4-backend",
                "error": str(e)
            }
        )

# Mount the main application
app.mount("/", main_app)

# Readiness probe endpoint (for Kubernetes or advanced deployments)
@app.get("/ready")
async def readiness_check() -> JSONResponse:
    """Check if the application is ready to serve traffic."""
    try:
        # Add readiness checks here (database connectivity, required services, etc.)
        return JSONResponse(
            status_code=200,
            content={
                "status": "ready",
                "service": "datahero4-backend"
            }
        )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "service": "datahero4-backend",
                "error": str(e)
            }
        )

# Mount the main application
app.mount("/", main_app)

# Development server runner
if __name__ == "__main__":
    try:
        import uvicorn
        
        # Get port from environment variable (supports Railway, Render, Heroku, etc.)
        port = int(os.getenv("PORT", 8000))
        host = os.getenv("HOST", "0.0.0.0")
        
        logger.info(f"Starting development server on {host}:{port}")
        
        uvicorn.run(
            app, 
            host=host, 
            port=port,
            log_level=os.getenv("LOG_LEVEL", "info").lower(),
            access_log=True
        )
        
    except ImportError:
        logger.error("❌ uvicorn not found. Install with: poetry add uvicorn")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1) 