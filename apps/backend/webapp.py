"""
Direct FastAPI webapp without mounting - includes all routes directly.
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI
import os
import logging

# Configure logging
logging.basicConfig(level=os.getenv("LOG_LEVEL", "INFO"))
logger = logging.getLogger(__name__)

# Define lifespan for resource management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    # Initialize resources on startup
    logger.info("🚀 DataHero4 Direct starting up...")
    yield
    # Clean up resources on shutdown
    logger.info("🔄 DataHero4 Direct shutting down...")

# Create FastAPI app instance with lifespan
app = FastAPI(
    title="DataHero4 Direct API",
    description="DataHero4 without mounting - direct routes",
    version="4.0.0-direct",
    lifespan=lifespan
)

# Import and include all routes directly instead of mounting
try:
    # DEBUG: Check if validation directory exists
    import os
    logger.info(f"🔍 Current directory: {os.getcwd()}")
    logger.info(f"🔍 src directory exists: {os.path.exists('src')}")
    logger.info(f"🔍 src/validation exists: {os.path.exists('src/validation')}")
    if os.path.exists('src'):
        logger.info(f"🔍 src contents: {os.listdir('src')}")

    from src.interfaces.api import app as main_app

    # Copy all routes from main_app to our app
    logger.info("📋 Copying routes from main app...")

    # Add all routes from main_app
    for route in main_app.routes:
        app.routes.append(route)

    # Copy middleware
    for middleware in main_app.user_middleware:
        app.add_middleware(middleware.cls, **middleware.options)

    logger.info("✅ Routes and middleware copied successfully")

except Exception as e:
    logger.error(f"❌ Failed to copy routes: {e}")
    logger.error("❌ CRITICAL: Cannot start without validation modules - they are required for:")
    logger.error("❌ - Temporal query validation")
    logger.error("❌ - Business rules validation")
    logger.error("❌ - SQL auto-correction")
    logger.error("❌ - Hybrid validation system")
    raise e  # Don't use fallback - fail fast to identify the real problem

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("PORT", 8000)))
