# Nixpacks configuration for DataHero4 Backend
# This forces Nixpacks to use Python provider

providers = ["python"]

[variables]
PYTHONPATH = "/app"
PYTHONUNBUFFERED = "1"

[phases.setup]
nixPkgs = ["python311", "postgresql"]

[phases.install]
cmds = ["pip install -r requirements.txt"]

[phases.build]
cmds = ["echo 'Build phase completed'"]

[start]
cmd = "uvicorn main:app --host 0.0.0.0 --port $PORT"
