name: CI Pipeline

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]

jobs:
  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Install dependencies
        working-directory: apps/backend
        run: poetry install --with dev

      - name: Run tests
        working-directory: apps/backend
        run: poetry run pytest tests/ -v --tb=short

      - name: Run linting
        working-directory: apps/backend
        run: poetry run ruff check src tests

  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install dependencies
        working-directory: apps/frontend
        run: npm install

      - name: Run linting
        working-directory: apps/frontend
        run: npm run lint

      - name: Run type checking
        working-directory: apps/frontend
        run: npm run typecheck

      - name: Run tests
        working-directory: apps/frontend
        run: npm test

      - name: Build
        working-directory: apps/frontend
        run: npm run build

  integration:
    name: Integration Tests
    needs: [test-backend, test-frontend]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup environment
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 10

      - name: Run integration tests
        run: |
          cd apps/backend
          poetry run pytest tests/integration -v 