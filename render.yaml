services:
  # Backend API Service
  - type: web
    name: datahero4-backend
    runtime: docker
    region: oregon
    plan: free
    dockerfilePath: ./Dockerfile
    healthCheckPath: /health
    autoDeploy: true
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO
      - key: PORT
        value: 8000
      - key: DATABASE_URL
        sync: false
      - key: DATABASE_URL_LEARNING
        fromDatabase:
          name: datahero4-learning-db
          property: connectionString
      - key: TOGETHER_API_KEY
        sync: false
      - key: ANTHROPIC_API_KEY
        sync: false
      - key: GOOGLE_API_KEY
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: REDIS_URL
        sync: false
      - key: DEFAULT_CLIENT_ID
        value: L2M
      - key: DEFAULT_SECTOR
        value: cambio

  # Frontend Web Service (SPA)
  - type: web
    name: datahero4-frontend
    runtime: node
    region: oregon
    plan: free
    buildCommand: npm install --ignore-scripts && npx vite build --config apps/frontend/vite.config.ts apps/frontend
    startCommand: cd apps/frontend && npm run preview -- --port $PORT --host 0.0.0.0
    autoDeploy: true
    envVars:
      - key: VITE_API_BASE_URL
        value: https://datahero4-backend.onrender.com
      - key: NODE_ENV
        value: production

# PostgreSQL databases
databases:
  - name: datahero4-learning-db
    databaseName: datahero4_learning
    user: datahero4_user
    plan: free # free tier para testes
    region: oregon